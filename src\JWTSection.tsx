import React, { useState } from 'react';

interface JWTSectionProps {
  title: string;
  content: string;
  copyText: string;
}

export default function JWTSection({ title, content, copyText }: JWTSectionProps) {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = async () => {
    if (copyText) {
      try {
        await navigator.clipboard.writeText(copyText);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 1000);
      } catch (err) {
        console.error('Failed to copy text: ', err);
        const textArea = document.createElement('textarea');
        textArea.value = copyText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 1000);
      }
    }
  };

  const textColor = title === 'Header' ? '#5cd3ff' : title === 'Payload' ? '#e2e8f0' : '#f4b719';

  return (
    <div style={{
      border: '1px solid #334155',
      borderRadius: '6px',
      marginBottom: '12px',
      backgroundColor: '#0f172a'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '8px 12px',
        backgroundColor: '#1e293b',
        borderBottom: '1px solid #334155',
        borderRadius: '6px 6px 0 0'
      }}>
        <span style={{ fontWeight: '600', fontSize: '14px', color: '#95a9cc' }}>{title}</span>
        <button
          onClick={handleCopy}
          style={{
            background: ` #1e293b`,
            border: '1px solid #334155',
            color: '#95a9cc',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            minWidth: '24px',
            minHeight: '30px'
          }}
        >
          {isCopied ? (
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
          ) : (
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
          )}
        </button>
      </div>
      <div style={{
        padding: '12px',
        fontFamily: 'monospace',
        fontSize: '13px',
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-word',
        color: textColor,
        maxHeight: '150px',
        overflowY: 'auto'
      }}>
        {content || `${title} will appear here...`}
      </div>
    </div>
  );
}
