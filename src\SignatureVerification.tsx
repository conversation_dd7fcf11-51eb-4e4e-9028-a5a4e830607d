import React, { useState } from 'react';
import { verifySignature } from './jwtUtils';

interface SignatureVerificationProps {
  jwtToken: string;
}

export default function SignatureVerification({ jwtToken }: SignatureVerificationProps) {
  const [secretKey, setSecretKey] = useState('');
  const [verificationStatus, setVerificationStatus] = useState<{
    status: 'pending' | 'valid' | 'invalid' | 'error';
    icon: string;
  }>({
    status: 'pending',
    icon: '⚠'
  });

  const handleSecretKeyChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSecretKey = e.target.value;
    setSecretKey(newSecretKey);

    if (newSecretKey.trim() === '') {
      setVerificationStatus({
        status: 'pending',
        icon: '⚠'
      });
      return;
    }

    if (!jwtToken || jwtToken.trim() === '') {
      setVerificationStatus({
        status: 'error',
        icon: '❌'
      });
      return;
    }

    //automatically verify the signature
    try {
      const isValid = await verifySignature(jwtToken, newSecretKey);

      if (isValid) {
        setVerificationStatus({
          status: 'valid',
          icon: '✅'
        });
      } else {
        setVerificationStatus({
          status: 'invalid',
          icon: '❌'
        });
      }
    } catch (error) {
      setVerificationStatus({
        status: 'error',
        icon: '❌'
      });
    }
  };

  return (
    <div className="signature-section">
      <div className="signature-header">
        <h4>Signature Verification</h4>
        <span className="optional-badge">Optional</span>
      </div>
      <div className="secret-key-box">
        <span>{verificationStatus.icon}</span>
        <input
          type="password"
          placeholder="Enter secret key for verification..."
          value={secretKey}
          className="secret-key-input"
          onChange={handleSecretKeyChange}
        />
      </div>
      </div>
  );
}
